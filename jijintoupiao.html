<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基金购买与定投计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .result {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #4facfe;
        }

        .result h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .risk-indicator {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .risk-low { background: #28a745; }
        .risk-medium { background: #ffc107; color: #333; }
        .risk-high { background: #dc3545; }

        .recommendation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .recommendation h3 {
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .chart-container {
            grid-column: 1 / -1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>基金购买与定投计算器</h1>
            <p>智能评估基金投资价值，计算定投收益</p>
        </div>

        <div class="main-content">
            <!-- 基金信息输入 -->
            <div class="section">
                <h2>基金基本信息</h2>
                <div class="form-group">
                    <label for="fundName">基金名称</label>
                    <input type="text" id="fundName" placeholder="请输入基金名称">
                </div>
                <div class="form-group">
                    <label for="fundCode">基金代码</label>
                    <input type="text" id="fundCode" placeholder="请输入基金代码">
                </div>
                <div class="form-group">
                    <label for="fundType">基金类型</label>
                    <select id="fundType">
                        <option value="">请选择基金类型</option>
                        <option value="stock">股票型基金</option>
                        <option value="bond">债券型基金</option>
                        <option value="mixed">混合型基金</option>
                        <option value="index">指数型基金</option>
                        <option value="money">货币型基金</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="currentPrice">当前净值</label>
                    <input type="number" id="currentPrice" step="0.0001" placeholder="请输入当前净值">
                </div>
                <div class="form-group">
                    <label for="yearReturn">近一年收益率 (%)</label>
                    <input type="number" id="yearReturn" step="0.01" placeholder="请输入近一年收益率">
                </div>
                <div class="form-group">
                    <label for="managementFee">管理费率 (%)</label>
                    <input type="number" id="managementFee" step="0.01" placeholder="请输入管理费率">
                </div>
            </div>

            <!-- 投资计划 -->
            <div class="section">
                <h2>投资计划</h2>
                <div class="form-group">
                    <label for="investmentType">投资方式</label>
                    <select id="investmentType">
                        <option value="lump">一次性投资</option>
                        <option value="regular">定期定投</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="investAmount">投资金额 (元)</label>
                    <input type="number" id="investAmount" placeholder="请输入投资金额">
                </div>
                <div class="form-group" id="regularInvestGroup" style="display: none;">
                    <label for="investPeriod">定投周期</label>
                    <select id="investPeriod">
                        <option value="monthly">每月</option>
                        <option value="weekly">每周</option>
                        <option value="biweekly">每两周</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="investDuration">投资期限 (年)</label>
                    <input type="number" id="investDuration" min="1" max="30" placeholder="请输入投资期限">
                </div>
                <div class="form-group">
                    <label for="expectedReturn">预期年化收益率 (%)</label>
                    <input type="number" id="expectedReturn" step="0.01" placeholder="请输入预期年化收益率">
                </div>
                <div class="form-group">
                    <label for="riskTolerance">风险承受能力</label>
                    <select id="riskTolerance">
                        <option value="conservative">保守型</option>
                        <option value="moderate">稳健型</option>
                        <option value="aggressive">积极型</option>
                    </select>
                </div>
                <button class="btn" onclick="calculateInvestment()">计算投资收益</button>
            </div>
        </div>

        <!-- 计算结果 -->
        <div class="chart-container" id="resultsContainer" style="display: none;">
            <h2>计算结果</h2>
            <div id="calculationResults"></div>
            <div id="riskAssessment"></div>
            <div id="recommendation"></div>
        </div>
    </div>

    <script>
        // 监听投资方式变化
        document.getElementById('investmentType').addEventListener('change', function() {
            const regularGroup = document.getElementById('regularInvestGroup');
            if (this.value === 'regular') {
                regularGroup.style.display = 'block';
            } else {
                regularGroup.style.display = 'none';
            }
        });

        // 主要计算函数
        function calculateInvestment() {
            // 获取输入值
            const fundName = document.getElementById('fundName').value;
            const fundCode = document.getElementById('fundCode').value;
            const fundType = document.getElementById('fundType').value;
            const currentPrice = parseFloat(document.getElementById('currentPrice').value);
            const yearReturn = parseFloat(document.getElementById('yearReturn').value);
            const managementFee = parseFloat(document.getElementById('managementFee').value);
            const investmentType = document.getElementById('investmentType').value;
            const investAmount = parseFloat(document.getElementById('investAmount').value);
            const investPeriod = document.getElementById('investPeriod').value;
            const investDuration = parseFloat(document.getElementById('investDuration').value);
            const expectedReturn = parseFloat(document.getElementById('expectedReturn').value);
            const riskTolerance = document.getElementById('riskTolerance').value;

            // 验证必填字段
            if (!fundName || !investAmount || !investDuration || !expectedReturn) {
                alert('请填写必要的信息！');
                return;
            }

            // 计算投资收益
            const results = calculateReturns(investmentType, investAmount, investPeriod, investDuration, expectedReturn, managementFee);

            // 风险评估
            const riskAssessment = assessRisk(fundType, yearReturn, riskTolerance, expectedReturn);

            // 生成购买建议
            const recommendation = generateRecommendation(riskAssessment, results, fundType, yearReturn);

            // 显示结果
            displayResults(results, riskAssessment, recommendation, fundName, fundCode);
        }

        // 计算投资收益
        function calculateReturns(investmentType, amount, period, duration, expectedReturn, managementFee = 0) {
            const annualReturn = expectedReturn / 100;
            const annualFee = managementFee / 100;
            const netReturn = annualReturn - annualFee;

            let results = {};

            if (investmentType === 'lump') {
                // 一次性投资
                const finalValue = amount * Math.pow(1 + netReturn, duration);
                const totalReturn = finalValue - amount;
                const returnRate = (totalReturn / amount) * 100;

                results = {
                    type: '一次性投资',
                    initialInvestment: amount,
                    totalInvestment: amount,
                    finalValue: finalValue,
                    totalReturn: totalReturn,
                    returnRate: returnRate,
                    annualizedReturn: expectedReturn
                };
            } else {
                // 定期定投
                let periodsPerYear;
                switch(period) {
                    case 'monthly': periodsPerYear = 12; break;
                    case 'weekly': periodsPerYear = 52; break;
                    case 'biweekly': periodsPerYear = 26; break;
                    default: periodsPerYear = 12;
                }

                const totalPeriods = duration * periodsPerYear;
                const periodReturn = netReturn / periodsPerYear;

                // 定投复利计算
                let finalValue = 0;
                for (let i = 0; i < totalPeriods; i++) {
                    finalValue += amount * Math.pow(1 + periodReturn, totalPeriods - i);
                }

                const totalInvestment = amount * totalPeriods;
                const totalReturn = finalValue - totalInvestment;
                const returnRate = (totalReturn / totalInvestment) * 100;

                results = {
                    type: '定期定投',
                    monthlyInvestment: amount,
                    totalInvestment: totalInvestment,
                    finalValue: finalValue,
                    totalReturn: totalReturn,
                    returnRate: returnRate,
                    annualizedReturn: expectedReturn,
                    totalPeriods: totalPeriods,
                    periodType: period
                };
            }

            return results;
        }

        // 风险评估
        function assessRisk(fundType, yearReturn, riskTolerance, expectedReturn) {
            let riskScore = 0;
            let riskFactors = [];

            // 基金类型风险评分
            const typeRisk = {
                'stock': 4,
                'mixed': 3,
                'index': 3,
                'bond': 2,
                'money': 1
            };
            riskScore += typeRisk[fundType] || 3;

            // 收益率波动风险
            if (Math.abs(yearReturn) > 30) {
                riskScore += 2;
                riskFactors.push('历史收益率波动较大');
            } else if (Math.abs(yearReturn) > 15) {
                riskScore += 1;
                riskFactors.push('历史收益率有一定波动');
            }

            // 预期收益率风险
            if (expectedReturn > 15) {
                riskScore += 2;
                riskFactors.push('预期收益率较高，风险相应增加');
            } else if (expectedReturn > 8) {
                riskScore += 1;
                riskFactors.push('预期收益率适中');
            }

            // 风险承受能力匹配度
            const toleranceScore = {
                'conservative': 1,
                'moderate': 2,
                'aggressive': 3
            };

            const userRiskScore = toleranceScore[riskTolerance];
            const fundRiskLevel = riskScore <= 3 ? 1 : riskScore <= 6 ? 2 : 3;

            let riskLevel, riskClass, matchStatus;
            if (riskScore <= 3) {
                riskLevel = '低风险';
                riskClass = 'risk-low';
            } else if (riskScore <= 6) {
                riskLevel = '中等风险';
                riskClass = 'risk-medium';
            } else {
                riskLevel = '高风险';
                riskClass = 'risk-high';
            }

            if (userRiskScore >= fundRiskLevel) {
                matchStatus = '风险匹配度良好';
            } else {
                matchStatus = '风险超出承受能力，建议谨慎投资';
                riskFactors.push('投资风险可能超出您的承受能力');
            }

            return {
                riskScore,
                riskLevel,
                riskClass,
                riskFactors,
                matchStatus,
                userRiskScore,
                fundRiskLevel
            };
        }